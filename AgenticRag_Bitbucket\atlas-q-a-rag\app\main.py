"""
FastAPI application for the Agentic RAG system.
"""

import logging
import os
import uuid
import asyncio
from datetime import datetime
from functools import lru_cache

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv
import pyodbc
import pandas as pd

from app.core.agentic_rag import AgenticRAG
from app.agents.langgraph_agent import LangGraphAgent
from app.models.api_models import (
    QueryRequest,
    BotInfo,
    BotsListResponse,
    DocumentProcessRequest,
    DocumentProcessResponse,
    SqlConnectionRequest,
    SqlConnectionResponse,
    SqlQueryRequest,
    SqlQueryResponse,
)
from app.core.logging_config import setup_logging, get_logger, get_api_logger
from app.core.logging_helpers import (
    log_api_request,
    QueryLogger,
    get_simple_logger_instance,
)
from app.document_processing.document_processor import DocumentProcessor

# Load environment variables
load_dotenv()

# Setup centralized logging
setup_logging(
    log_dir=os.getenv("LOG_DIR", "logs"),
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    enable_console=os.getenv("DEBUG", "False").lower() == "true",
    enable_json_logs=True,
)

# Get loggers
logger = get_logger(__name__)
api_logger = get_api_logger()
simple_logger = get_simple_logger_instance()

# Initialize the AgenticRAG system
logger.info("Initializing AgenticRAG system...")
agentic_rag = AgenticRAG()
logger.info("AgenticRAG system initialized successfully")

# Initialize the DocumentProcessor
logger.info("Initializing DocumentProcessor...")
document_processor = DocumentProcessor()
logger.info("DocumentProcessor initialized successfully")

# Create the FastAPI application
app = FastAPI(
    title="Agentic RAG API",
    description="API for the Agentic RAG system",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Middleware to log all requests
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests."""
    request_id = str(uuid.uuid4())[:8]
    start_time = datetime.now()

    # Log request
    api_logger.info(
        f"[{request_id}] {request.method} {request.url.path}",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else "unknown",
        },
    )

    # Process request
    response = await call_next(request)

    # Calculate duration
    duration = (datetime.now() - start_time).total_seconds()

    # Log response
    api_logger.info(
        f"[{request_id}] Response: {response.status_code} ({duration:.3f}s)",
        extra={
            "request_id": request_id,
            "status_code": response.status_code,
            "duration": duration,
        },
    )

    # Sade log
    simple_logger.api_request(
        request.method, request.url.path, response.status_code, duration
    )

    return response


# Dependency to get the AgenticRAG instance
def get_agentic_rag() -> AgenticRAG:
    return agentic_rag


# Dependency to get the DocumentProcessor instance
def get_document_processor() -> DocumentProcessor:
    return document_processor


@app.get("/", tags=["Health"])
async def root():
    """Root endpoint for health check."""
    return {"status": "ok", "message": "Agentic RAG API is running"}


@app.get("/bots", response_model=BotsListResponse, tags=["Bots"])
async def list_bots(rag: AgenticRAG = Depends(get_agentic_rag)):
    """List all available bots."""
    bot_names = rag.get_bot_names()
    bots = []

    for bot_name in bot_names:
        bot = rag.get_bot(bot_name)
        if bot:
            config = bot["config"]
            bots.append(
                BotInfo(
                    name=config.name,
                    description=config.description,
                    tools=[tool.type for tool in config.tools if tool.enabled],
                    metadata=config.metadata,
                )
            )

    return BotsListResponse(bots=bots)


@app.get("/bots/{bot_name}", response_model=BotInfo, tags=["Bots"])
async def get_bot_info(bot_name: str, rag: AgenticRAG = Depends(get_agentic_rag)):
    """Get information about a specific bot."""
    bot = rag.get_bot(bot_name)
    if not bot:
        raise HTTPException(status_code=404, detail=f"Bot not found: {bot_name}")

    config = bot["config"]
    return BotInfo(
        name=config.name,
        description=config.description,
        tools=[tool.type for tool in config.tools if tool.enabled],
        metadata=config.metadata,
    )


@app.post("/bots/{bot_name}/query", tags=["Queries"])
async def query_bot(
    bot_name: str, request: QueryRequest, rag: AgenticRAG = Depends(get_agentic_rag)
):
    """Query a specific bot."""
    # Initialize query logger
    query_logger_instance = QueryLogger(bot_name=bot_name)

    try:
        # Log query start
        query_logger_instance.log_query_start(
            query_text=request.query, user_id=getattr(request, "user_id", None)
        )

        # Sade log - sorgu alındı
        simple_logger.query_received(
            bot_name, request.query, getattr(request, "user_id", None)
        )

        # Check if bot exists
        bot = rag.get_bot(bot_name)
        if not bot:
            logger.warning(f"Bot not found: {bot_name}")
            simple_logger.error_occurred("Bot bulunamadı", f"Bot: {bot_name}")
            raise HTTPException(status_code=404, detail=f"Bot not found: {bot_name}")

        # Log bot found
        logger.info(f"Processing query for bot: {bot_name}")

        # Process query
        start_time = datetime.now()
        response = await rag.process_query(bot_name, request)
        query_duration = (datetime.now() - start_time).total_seconds()

        # Convert to dict and then to JSON with ensure_ascii=False to preserve Turkish characters
        response_dict = response.dict()

        # Ensure all values are JSON serializable
        def ensure_serializable(obj):
            if isinstance(obj, dict):
                return {k: ensure_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [ensure_serializable(item) for item in obj]
            elif hasattr(obj, "keys") and callable(obj.keys):
                # Convert dict-like objects (like ResultProxy.keys()) to lists
                return list(obj)
            else:
                return obj

        # Apply the serialization fix to the response dictionary
        serializable_response = ensure_serializable(response_dict)

        # Log successful response
        response_length = len(str(serializable_response))
        query_logger_instance.log_response_generation(
            response_length=response_length,
            duration=0,  # Duration will be calculated in log_query_complete
        )

        # Log query completion
        query_logger_instance.log_query_complete(success=True)

        # Sade log - sorgu tamamlandı
        simple_logger.query_completed(bot_name, query_duration, True, response_length)

        logger.info(
            f"Query processed successfully for bot: {bot_name}, response length: {response_length}"
        )

        # Return a custom JSONResponse with proper encoding
        return JSONResponse(
            content=serializable_response, media_type="application/json; charset=utf-8"
        )
    except ValueError as e:
        error_msg = f"Validation error for bot {bot_name}: {str(e)}"
        logger.error(error_msg)
        query_logger_instance.log_query_complete(success=False, error=error_msg)
        simple_logger.error_occurred("Doğrulama hatası", str(e), bot_name)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        error_msg = f"Error processing query for bot {bot_name}: {str(e)}"
        logger.error(error_msg)
        import traceback

        full_traceback = traceback.format_exc()
        logger.error(f"Full traceback: {full_traceback}")

        # Log query failure
        query_logger_instance.log_query_complete(success=False, error=error_msg)
        simple_logger.error_occurred("Sorgu işleme hatası", str(e), bot_name)

        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/bots/{bot_name}/clear-memory", tags=["Queries"])
async def clear_memory(
    bot_name: str, session_id: str = None, rag: AgenticRAG = Depends(get_agentic_rag)
):
    """Clear the memory for a specific session."""
    try:
        logger.info(f"Clearing memory for bot: {bot_name}, session: {session_id}")

        bot = rag.get_bot(bot_name)
        if not bot:
            logger.warning(f"Bot not found for memory clear: {bot_name}")
            raise HTTPException(status_code=404, detail=f"Bot not found: {bot_name}")

        agent: LangGraphAgent = bot["agent"]

        if session_id:
            # Clear memory for a specific session
            agent.memory_manager.clear_memory(session_id)
            logger.info(
                f"Memory cleared successfully for bot: {bot_name}, session: {session_id}"
            )
            # Sade log
            simple_logger.memory_cleared(bot_name, session_id)
            return {
                "status": "ok",
                "message": f"Memory cleared for session: {session_id}",
            }
        else:
            # If no session_id is provided, return an error
            logger.warning(
                f"Memory clear attempted without session_id for bot: {bot_name}"
            )
            raise HTTPException(status_code=400, detail="session_id is required")
    except Exception as e:
        error_msg = (
            f"Error clearing memory for bot {bot_name}, session {session_id}: {str(e)}"
        )
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=f"Error clearing memory: {str(e)}")


@app.post(
    "/process-documents", response_model=DocumentProcessResponse, tags=["Documents"]
)
async def process_documents(
    request: DocumentProcessRequest,
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Process documents in a directory and store them in a collection."""
    try:
        logger.info(f"Processing documents in directory: {request.directory_path}")
        logger.info(f"Collection name: {request.collection_name}")

        # Process the directory
        result = processor.process_directory(
            directory_path=request.directory_path,
            collection_name=request.collection_name,
            recursive=request.recursive,
            custom_metadata=request.custom_metadata,
        )

        if result["success"]:
            message = f"Successfully processed {result['successful_count']} files"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} failed)"

            logger.info(f"Document processing completed: {message}")

            return DocumentProcessResponse(
                success=True,
                message=message,
                directory_path=result["directory_path"],
                collection_name=result["collection_name"],
                total_files=result["total_files"],
                successful_count=result["successful_count"],
                failed_count=result["failed_count"],
                details={"results": result["results"]},
            )
        else:
            logger.error(
                f"Document processing failed: {result.get('error', 'Unknown error')}"
            )
            return DocumentProcessResponse(
                success=False,
                message="Document processing failed",
                error=result.get("error", "Unknown error"),
            )

    except Exception as e:
        error_msg = f"Error processing documents: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Processing traceback: {traceback.format_exc()}")

        return DocumentProcessResponse(
            success=False, message="Document processing failed", error=error_msg
        )


@app.post("/reload", tags=["Admin"])
async def reload_bots(rag: AgenticRAG = Depends(get_agentic_rag)):
    """Reload all bot configurations."""
    try:
        logger.info("Starting bot configuration reload...")

        # Reload configurations
        rag.config_loader.reload_configs()
        logger.info("Bot configurations reloaded successfully")

        # Reload bots
        rag._load_bots()
        logger.info("Bots reloaded successfully")

        # Sade log
        bot_count = len(rag.get_bot_names())
        simple_logger.config_reloaded(True, bot_count)

        return {"status": "ok", "message": "Bots reloaded successfully"}
    except Exception as e:
        error_msg = f"Error reloading bots: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Reload traceback: {traceback.format_exc()}")
        simple_logger.config_reloaded(False)
        raise HTTPException(status_code=500, detail=f"Error reloading bots: {str(e)}")


@app.post(
    "/test-sql-connection", response_model=SqlConnectionResponse, tags=["Database"]
)
async def test_sql_connection(request: SqlConnectionRequest):
    """Test SQL database connection."""
    try:
        logger.info("Testing SQL database connection...")

        # Test connection
        conn = pyodbc.connect(request.connection_string)
        conn.close()

        logger.info("✅ SQL connection successful")
        # Use a custom log message for SQL connection success
        simple_logger.logger.info("🔗 SQL BAĞLANTI TESTİ | ✅ BAŞARILI")

        return SqlConnectionResponse(success=True, message="✅ Bağlantı başarılı.")

    except Exception as e:
        error_msg = f"❌ Bağlantı başarısız: {str(e)}"
        logger.error(f"SQL connection test failed: {str(e)}")
        simple_logger.error_occurred("SQL bağlantı hatası", str(e))

        return SqlConnectionResponse(success=False, message=error_msg, error=str(e))


@app.post("/test-sql-query", response_model=SqlQueryResponse, tags=["Database"])
async def test_sql_query(request: SqlQueryRequest):
    """Test SQL database query and list tables with sample data."""
    try:
        logger.info("Testing SQL database query...")

        # Test connection first
        conn = pyodbc.connect(request.connection_string)
        logger.info("✅ Bağlantı başarılı.")

        # Get list of tables
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT TABLE_SCHEMA, TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
        """
        )
        tables = cursor.fetchall()

        all_dataframes = {}
        table_names = []

        # Get first 5 rows from each table
        for schema, table in tables:
            table_name = f"{schema}.{table}"
            table_names.append(table_name)
            query = f"SELECT TOP 5 * FROM [{schema}].[{table}]"
            try:
                df = pd.read_sql(query, conn)
                all_dataframes[table_name] = df
                logger.info(f"✅ {table_name} yüklendi ({len(df)} satır).")
            except Exception as e:
                logger.warning(f"❌ {table_name} yüklenemedi: {e}")

        conn.close()

        # Prepare sample data from first table
        sample_data = None
        if all_dataframes:
            first_table = list(all_dataframes.keys())[0]
            first_df = all_dataframes[first_table]
            sample_data = {
                "table_name": first_table,
                "row_count": len(first_df),
                "columns": list(first_df.columns),
                "sample_rows": first_df.to_dict("records"),
            }

        success_message = f"✅ Sorgu başarılı. {len(table_names)} tablo yüklendi."
        logger.info(success_message)
        # Use a custom log message for SQL query success
        simple_logger.logger.info(
            f"📊 SQL SORGU TESTİ | ✅ BAŞARILI | {len(table_names)} tablo yüklendi"
        )

        return SqlQueryResponse(
            success=True,
            message=success_message,
            tables=table_names,
            sample_data=sample_data,
        )

    except Exception as e:
        error_msg = f"❌ Sorgu başarısız: {str(e)}"
        logger.error(f"SQL query test failed: {str(e)}")
        simple_logger.error_occurred("SQL sorgu hatası", str(e))

        return SqlQueryResponse(success=False, message=error_msg, error=str(e))


def start():
    """Start the FastAPI application with Uvicorn."""
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "False").lower() == "true"

    # Performance optimizations
    workers = int(os.getenv("WORKERS", 4))  # Multiple workers for concurrent requests
    max_requests = int(
        os.getenv("MAX_REQUESTS", 1000)
    )  # Restart worker after N requests
    max_requests_jitter = int(
        os.getenv("MAX_REQUESTS_JITTER", 50)
    )  # Add jitter to prevent thundering herd

    logger.info("Starting Agentic RAG API server...")
    logger.info(f"Host: {host}")
    logger.info(f"Port: {port}")
    logger.info(f"Debug mode: {debug}")
    logger.info(f"Workers: {workers}")
    logger.info(f"Max requests per worker: {max_requests}")
    logger.info(f"Log directory: {os.getenv('LOG_DIR', 'logs')}")

    # Sade log - sistem başlatılıyor
    simple_logger.system_start(host, port, debug)

    try:
        if debug:
            # Development mode - single worker with reload
            uvicorn.run(
                "app.main:app",
                host=host,
                port=port,
                reload=True,
                workers=1,
            )
        else:
            # Production mode - multiple workers for better concurrency
            uvicorn.run(
                "app.main:app",
                host=host,
                port=port,
                workers=workers,
                max_requests=max_requests,
                max_requests_jitter=max_requests_jitter,
                access_log=False,  # Disable access log for better performance
            )
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
        raise


if __name__ == "__main__":
    start()
