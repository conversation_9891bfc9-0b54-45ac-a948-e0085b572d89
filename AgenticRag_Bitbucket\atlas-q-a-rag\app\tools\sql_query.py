"""
SQL query tool for the Agentic RAG system.
"""

import logging
import os
import asyncio
import threading
from typing import Any, Dict
from queue import Queue, Empty
from contextlib import contextmanager

import pyodbc
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

from app.tools.base import BaseTool
from app.core.logging_helpers import log_tool_execution

logger = logging.getLogger(__name__)


class PyODBCConnectionPool:
    """Simple connection pool for pyodbc connections."""

    def __init__(self, connection_string: str, pool_size: int = 10):
        self.connection_string = connection_string
        self.pool_size = pool_size
        self.pool = Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self._initialize_pool()

    def _initialize_pool(self):
        """Initialize the connection pool."""
        for _ in range(self.pool_size):
            try:
                conn = pyodbc.connect(self.connection_string, timeout=30)
                self.pool.put(conn)
            except Exception as e:
                logger.error(f"Failed to create connection for pool: {e}")

    @contextmanager
    def get_connection(self):
        """Get a connection from the pool."""
        conn = None
        try:
            # Try to get a connection from pool
            try:
                conn = self.pool.get(timeout=5)
            except Empty:
                # If pool is empty, create a new connection
                conn = pyodbc.connect(self.connection_string, timeout=30)

            # Test connection before use
            try:
                conn.execute("SELECT 1")
            except:
                # Connection is stale, create a new one
                try:
                    conn.close()
                except:
                    pass
                conn = pyodbc.connect(self.connection_string, timeout=30)

            yield conn

        except Exception as e:
            logger.error(f"Error with connection: {e}")
            # Close bad connection
            if conn:
                try:
                    conn.close()
                except:
                    pass
            raise
        finally:
            # Return connection to pool if it's still good
            if conn:
                try:
                    # Test if connection is still alive
                    conn.execute("SELECT 1")
                    # Put back in pool if there's space
                    try:
                        self.pool.put_nowait(conn)
                    except:
                        # Pool is full, close the connection
                        conn.close()
                except:
                    # Connection is bad, close it
                    try:
                        conn.close()
                    except:
                        pass


class SQLQueryTool(BaseTool):
    """Tool for querying SQL databases."""

    def initialize(self) -> None:
        """Initialize the SQL query tool."""
        # Check for environment variable first, then config, then default
        env_connection_string = os.getenv("SQL_CONNECTION_STRING")
        self.raw_connection_string = (
            env_connection_string
            if env_connection_string
            else self.config.get("connection_string", "sqlite:///default.db")
        )

        logger.info(
            f"Raw connection string source: {'ENV' if env_connection_string else 'CONFIG'}"
        )
        logger.info(f"Raw connection string: {self.raw_connection_string}")

        # Store both raw and converted connection strings
        self.connection_string = self._convert_connection_string(
            self.raw_connection_string
        )

        logger.info(f"Converted connection string: {self.connection_string}")

        self.max_results = self.config.get("max_results", 100)
        self.allowed_tables = self.config.get("allowed_tables", [])
        self.model_name = self.config.get("model", "gpt-4.1-mini")
        self.temperature = self.config.get("temperature", 0.0)

        # Detect database type from connection string
        self.db_type = self._detect_database_type(self.connection_string)
        logger.info(f"Detected database type: {self.db_type}")

        # Try to initialize SQLAlchemy engine first
        self.engine = None
        self.use_direct_pyodbc = False
        self.pyodbc_pool = None

        # Check if this is an ODBC connection string
        is_odbc = self._is_odbc_connection_string(self.raw_connection_string)
        logger.info(f"Is ODBC connection string: {is_odbc}")

        try:
            # Optimized connection pool settings for better concurrency
            engine_kwargs = {
                "pool_size": 20,  # Number of connections to maintain in pool
                "max_overflow": 30,  # Additional connections beyond pool_size
                "pool_timeout": 30,  # Timeout to get connection from pool
                "pool_recycle": 3600,  # Recycle connections after 1 hour
                "pool_pre_ping": True,  # Validate connections before use
                "connect_args": {
                    "timeout": 30,
                    "autocommit": True,
                    "check_same_thread": False,  # Allow sharing connections between threads
                },
            }

            logger.info("Attempting to create SQLAlchemy engine...")
            self.engine = create_engine(self.connection_string, **engine_kwargs)
            logger.info(
                f"SQLAlchemy engine created successfully: {self.connection_string}"
            )

            # Test the connection with a simple query
            try:
                logger.info("Testing SQLAlchemy connection...")
                with self.engine.connect() as connection:
                    connection.execute(text("SELECT 1"))
                logger.info("SQLAlchemy connection test successful")

                # Get table schemas for better query generation
                logger.info("Getting table schemas...")
                self.table_schemas = self._get_table_schemas()
                logger.info(f"Retrieved {len(self.table_schemas)} table schemas")

            except Exception as connection_error:
                logger.warning(
                    f"SQLAlchemy connection test failed: {str(connection_error)}"
                )
                logger.warning(f"Connection error type: {type(connection_error)}")
                raise SQLAlchemyError("Connection test failed") from connection_error

        except (SQLAlchemyError, Exception) as e:
            logger.warning(f"SQLAlchemy connection failed: {str(e)}")
            logger.warning(f"SQLAlchemy error type: {type(e)}")
            logger.info("Falling back to direct pyodbc connection")

            # Reset engine
            self.engine = None

            # Fall back to direct pyodbc if SQLAlchemy fails
            if is_odbc:
                try:
                    logger.info("Attempting direct pyodbc connection...")
                    # Test direct pyodbc connection
                    conn = pyodbc.connect(self.raw_connection_string, timeout=30)
                    logger.info("Direct pyodbc connection successful")
                    conn.close()
                    self.use_direct_pyodbc = True

                    # Initialize connection pool for better performance
                    logger.info("Initializing pyodbc connection pool...")
                    self.pyodbc_pool = PyODBCConnectionPool(
                        self.raw_connection_string, pool_size=10
                    )

                    logger.info("Getting table schemas with pyodbc...")
                    self.table_schemas = self._get_table_schemas_pyodbc()
                    logger.info(
                        f"Successfully initialized direct pyodbc connection with {len(self.table_schemas)} table schemas"
                    )
                except Exception as pyodbc_error:
                    logger.error(
                        f"Direct pyodbc connection also failed: {str(pyodbc_error)}"
                    )
                    logger.error(f"PyODBC error type: {type(pyodbc_error)}")
                    self.table_schemas = {}
                    # Don't raise here - let the tool continue with limited functionality
            else:
                logger.error("Cannot use direct pyodbc with non-ODBC connection string")
                self.table_schemas = {}

        # Log final initialization status
        if self.engine:
            logger.info("SQL Query Tool initialized with SQLAlchemy engine")
        elif self.use_direct_pyodbc:
            logger.info("SQL Query Tool initialized with direct pyodbc connection")
        else:
            logger.warning(
                "SQL Query Tool initialization failed - no working connection"
            )

        # Initialize LLM for query conversion
        try:
            self.llm = ChatOpenAI(model=self.model_name, temperature=self.temperature)
            logger.info(f"Initialized LLM for SQL query conversion: {self.model_name}")
        except Exception as e:
            logger.error(f"Error initializing LLM: {str(e)}")
            self.llm = None

    def _convert_connection_string(self, connection_string: str) -> str:
        """
        Convert ODBC connection string to SQLAlchemy format if needed.

        Args:
            connection_string: The raw connection string

        Returns:
            SQLAlchemy compatible connection string
        """
        # If it's already a SQLAlchemy URL, return as is
        if "://" in connection_string:
            return connection_string

        # If it's an ODBC connection string, convert it
        if connection_string.startswith("DRIVER="):
            try:
                # Parse ODBC connection string
                params = {}
                for part in connection_string.split(";"):
                    if "=" in part:
                        key, value = part.split("=", 1)
                        params[key.upper()] = value

                # Extract connection parameters
                server = params.get("SERVER", "")
                database = params.get("DATABASE", "")
                uid = params.get("UID", "")
                pwd = params.get("PWD", "")
                driver = params.get("DRIVER", "")
                trust_cert = params.get("TRUSTSERVERCERTIFICATE", "no")

                # Build SQLAlchemy connection string for SQL Server
                if "SQL Server" in driver:
                    # URL encode the password and other special characters
                    import urllib.parse

                    pwd_encoded = urllib.parse.quote_plus(pwd)
                    uid_encoded = urllib.parse.quote_plus(uid)
                    # Don't encode the server name - it causes issues with instance names
                    # server_encoded = urllib.parse.quote_plus(server)

                    sqlalchemy_url = f"mssql+pyodbc://{uid_encoded}:{pwd_encoded}@{server}/{database}?driver=ODBC+Driver+17+for+SQL+Server"

                    if trust_cert.lower() == "yes":
                        sqlalchemy_url += "&TrustServerCertificate=yes"

                    logger.info("Converted ODBC connection string to SQLAlchemy format")
                    return sqlalchemy_url

            except Exception as e:
                logger.error(f"Error converting ODBC connection string: {str(e)}")
                return connection_string

        return connection_string

    def _is_odbc_connection_string(self, connection_string: str) -> bool:
        """
        Check if the connection string is an ODBC connection string.

        Args:
            connection_string: The connection string to check

        Returns:
            True if it's an ODBC connection string
        """
        return connection_string.startswith("DRIVER=")

    def _get_table_schemas_pyodbc(self) -> Dict[str, Dict[str, Any]]:
        """
        Get table schemas using direct pyodbc connection.

        Returns:
            A dictionary mapping table names to their schema information
        """
        if not self.use_direct_pyodbc:
            return {}

        try:
            # Use connection pool if available, otherwise create new connection
            if self.pyodbc_pool:
                with self.pyodbc_pool.get_connection() as conn:
                    cursor = conn.cursor()
                    schemas = {}

                    # Get all table names
                    cursor.execute(
                        """
                        SELECT TABLE_NAME
                        FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_TYPE = 'BASE TABLE'
                        ORDER BY TABLE_NAME
                    """
                    )

                    table_names = [row[0] for row in cursor.fetchall()]

                    # Filter tables if allowed_tables is specified
                    if self.allowed_tables:
                        table_names = [
                            t for t in table_names if t in self.allowed_tables
                        ]

                    # Get schema for each table
                    for table_name in table_names:
                        try:
                            cursor.execute(
                                f"""
                                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                                FROM INFORMATION_SCHEMA.COLUMNS
                                WHERE TABLE_NAME = '{table_name}'
                                ORDER BY ORDINAL_POSITION
                            """
                            )

                            columns = []
                            for row in cursor.fetchall():
                                columns.append(
                                    {
                                        "name": row[0],
                                        "type": row[1],
                                        "nullable": row[2] == "YES",
                                    }
                                )

                            schemas[table_name] = {
                                "columns": columns,
                                "primary_key": [],  # Could be enhanced to get PK info
                                "foreign_keys": [],  # Could be enhanced to get FK info
                            }

                        except Exception as table_error:
                            logger.warning(
                                f"Could not get schema for table {table_name}: {str(table_error)}"
                            )
                            continue
            else:
                # Fallback to direct connection
                conn = pyodbc.connect(self.raw_connection_string, timeout=30)
                cursor = conn.cursor()
                schemas = {}

                # Get all table names
                cursor.execute(
                    """
                    SELECT TABLE_NAME
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME
                """
                )

                table_names = [row[0] for row in cursor.fetchall()]

                # Filter tables if allowed_tables is specified
                if self.allowed_tables:
                    table_names = [t for t in table_names if t in self.allowed_tables]

                # Get schema for each table
                for table_name in table_names:
                    try:
                        cursor.execute(
                            f"""
                            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                            FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_NAME = '{table_name}'
                            ORDER BY ORDINAL_POSITION
                        """
                        )

                        columns = []
                        for row in cursor.fetchall():
                            columns.append(
                                {
                                    "name": row[0],
                                    "type": row[1],
                                    "nullable": row[2] == "YES",
                                }
                            )

                        schemas[table_name] = {
                            "columns": columns,
                            "primary_key": [],  # Could be enhanced to get PK info
                            "foreign_keys": [],  # Could be enhanced to get FK info
                        }

                    except Exception as table_error:
                        logger.warning(
                            f"Could not get schema for table {table_name}: {str(table_error)}"
                        )
                        continue

                conn.close()

            logger.info(
                f"Retrieved schema information for {len(schemas)} tables using pyodbc"
            )
            return schemas

        except Exception as e:
            logger.error(f"Error getting table schemas with pyodbc: {str(e)}")
            return {}

    def _detect_database_type(self, connection_string: str) -> str:
        """
        Detect the database type from the connection string.

        Args:
            connection_string: The database connection string

        Returns:
            The database type (sqlite, postgresql, mysql, mssql)
        """
        connection_string = connection_string.lower()

        if connection_string.startswith("sqlite"):
            return "sqlite"
        elif connection_string.startswith("postgresql"):
            return "postgresql"
        elif connection_string.startswith("mysql"):
            return "mysql"
        elif connection_string.startswith("mssql"):
            return "mssql"
        else:
            # Default to sqlite if unknown
            return "sqlite"

    def _get_database_syntax_guide(self) -> str:
        """
        Get database-specific syntax guidelines.

        Returns:
            A string containing database-specific syntax guidelines
        """
        if self.db_type == "mssql":
            return """
            SQL Server Syntax Guidelines:
            - Use TOP N instead of LIMIT N (e.g., SELECT TOP 10 * FROM table)
            - Use OFFSET/FETCH for pagination (e.g., ORDER BY column OFFSET 0 ROWS FETCH NEXT 10 ROWS ONLY)
            - Use square brackets for identifiers with spaces or special characters [Table Name]
            - Use GETDATE() for current date/time
            - Use LEN() instead of LENGTH() for string length
            - Use CHARINDEX() instead of INSTR() for string position
            - Use ISNULL() instead of COALESCE() for null handling
            - String concatenation with + operator or CONCAT() function
            """
        elif self.db_type == "postgresql":
            return """
            PostgreSQL Syntax Guidelines:
            - Use LIMIT N for limiting results
            - Use OFFSET N for pagination
            - Use double quotes for case-sensitive identifiers "Table_Name"
            - Use NOW() for current timestamp
            - Use LENGTH() for string length
            - Use POSITION() for string position
            - Use COALESCE() for null handling
            - String concatenation with || operator or CONCAT() function
            """
        elif self.db_type == "mysql":
            return """
            MySQL Syntax Guidelines:
            - Use LIMIT N for limiting results
            - Use LIMIT N OFFSET M for pagination
            - Use backticks for identifiers `table_name`
            - Use NOW() for current timestamp
            - Use LENGTH() or CHAR_LENGTH() for string length
            - Use LOCATE() for string position
            - Use IFNULL() or COALESCE() for null handling
            - String concatenation with CONCAT() function
            """
        else:  # sqlite
            return """
            SQLite Syntax Guidelines:
            - Use LIMIT N for limiting results
            - Use LIMIT N OFFSET M for pagination
            - Use double quotes or square brackets for identifiers
            - Use datetime('now') for current timestamp
            - Use LENGTH() for string length
            - Use INSTR() for string position
            - Use IFNULL() or COALESCE() for null handling
            - String concatenation with || operator
            """

    @log_tool_execution
    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Execute a SQL query.

        Args:
            query: The user query (will be processed to extract SQL query)
            **kwargs: Additional arguments
                - sql_query: The SQL query to execute (optional)
                - max_results: Maximum number of results to return (optional)
                - use_llm: Whether to use LLM for query conversion (default: True)

        Returns:
            A dictionary containing the query results
        """
        logger.info(f"SQL Query Tool execute called with query: {query}")
        logger.info(f"Engine available: {self.engine is not None}")
        logger.info(f"Direct pyodbc available: {self.use_direct_pyodbc}")

        if not self.engine and not self.use_direct_pyodbc:
            error_msg = "SQL engine not initialized and pyodbc fallback not available"
            logger.error(error_msg)
            logger.error(
                "This usually means both SQLAlchemy and pyodbc connection attempts failed during initialization"
            )
            return {
                "success": False,
                "error": error_msg,
                "results": [],
                "debug_info": {
                    "engine_available": False,
                    "pyodbc_available": False,
                    "raw_connection_string": getattr(
                        self, "raw_connection_string", "Not set"
                    ),
                    "converted_connection_string": getattr(
                        self, "connection_string", "Not set"
                    ),
                },
            }

        try:
            # Get the SQL query to execute
            sql_query = kwargs.get("sql_query")
            use_llm = kwargs.get("use_llm", True)

            # If no SQL query is provided, try to convert natural language to SQL
            if not sql_query and use_llm and self.llm is not None:
                logger.info(f"Converting natural language to SQL query: {query}")
                conversion_result = await self._convert_nl_to_sql_query(query)

                if conversion_result["success"]:
                    sql_query = conversion_result["sql_query"]
                    logger.info(f"LLM generated SQL query: {sql_query}")
                else:
                    logger.warning(
                        f"LLM conversion failed: {conversion_result.get('error', 'Unknown error')}"
                    )

            # If we still don't have a SQL query, return an error
            if not sql_query:
                return {
                    "success": False,
                    "error": "Could not generate SQL query from natural language",
                    "results": [],
                }

            # Get the maximum number of results to return
            max_results = kwargs.get("max_results", self.max_results)

            # Execute the query using appropriate method
            if self.use_direct_pyodbc:
                return self._execute_with_pyodbc(
                    sql_query, max_results, use_llm, kwargs
                )
            else:
                return self._execute_with_sqlalchemy(
                    sql_query, max_results, use_llm, kwargs
                )

        except Exception as e:
            logger.error(f"Error executing SQL query: {str(e)}")
            return {"success": False, "error": str(e), "results": []}

    def _execute_with_sqlalchemy(
        self, sql_query: str, max_results: int, use_llm: bool, kwargs: Dict
    ) -> Dict[str, Any]:
        """Execute query using SQLAlchemy."""
        try:
            # Execute the query
            with self.engine.connect() as connection:
                result = connection.execute(text(sql_query))

                # Get column names and convert to list for JSON serialization
                columns = list(result.keys())

                # Get results
                rows = result.fetchmany(max_results)

                # Format the results
                results = []
                for row in rows:
                    results.append(dict(zip(columns, row)))

            return {
                "success": True,
                "query": sql_query,
                "results": results,
                "count": len(results),
                "columns": columns,
                "llm_used": use_llm
                and self.llm is not None
                and not kwargs.get("sql_query"),
            }
        except SQLAlchemyError as e:
            logger.error(f"SQLAlchemy error executing query: {str(e)}")
            return {"success": False, "error": str(e), "results": []}

    def _execute_with_pyodbc(
        self, sql_query: str, max_results: int, use_llm: bool, kwargs: Dict
    ) -> Dict[str, Any]:
        """Execute query using direct pyodbc with connection pool."""
        try:
            # Use connection pool if available, otherwise create new connection
            if self.pyodbc_pool:
                with self.pyodbc_pool.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(sql_query)

                    # Get column names
                    columns = [column[0] for column in cursor.description]

                    # Get results
                    rows = cursor.fetchmany(max_results)

                    # Format the results
                    results = []
                    for row in rows:
                        results.append(dict(zip(columns, row)))
            else:
                # Fallback to direct connection
                conn = pyodbc.connect(self.raw_connection_string, timeout=30)
                cursor = conn.cursor()
                cursor.execute(sql_query)

                # Get column names
                columns = [column[0] for column in cursor.description]

                # Get results
                rows = cursor.fetchmany(max_results)

                # Format the results
                results = []
                for row in rows:
                    results.append(dict(zip(columns, row)))

                conn.close()

            return {
                "success": True,
                "query": sql_query,
                "results": results,
                "count": len(results),
                "columns": columns,
                "llm_used": use_llm
                and self.llm is not None
                and not kwargs.get("sql_query"),
            }
        except Exception as e:
            logger.error(f"PyODBC error executing query: {str(e)}")
            return {"success": False, "error": str(e), "results": []}

    def _get_table_schemas(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the schema information for all tables in the database.

        Returns:
            A dictionary mapping table names to their schema information
        """
        if not self.engine:
            logger.error("Cannot get table schemas: SQL engine not initialized")
            return {}

        try:
            inspector = inspect(self.engine)
            schemas = {}

            # Get all table names
            table_names = inspector.get_table_names()

            # Filter tables if allowed_tables is specified
            if self.allowed_tables:
                table_names = [t for t in table_names if t in self.allowed_tables]

            # Get schema for each table
            for table_name in table_names:
                columns = []
                for column in inspector.get_columns(table_name):
                    columns.append(
                        {
                            "name": column["name"],
                            "type": str(column["type"]),
                            "nullable": column.get("nullable", True),
                        }
                    )

                # Get primary key information
                pk_columns = inspector.get_pk_constraint(table_name).get(
                    "constrained_columns", []
                )

                # Get foreign key information
                foreign_keys = []
                for fk in inspector.get_foreign_keys(table_name):
                    foreign_keys.append(
                        {
                            "referred_table": fk["referred_table"],
                            "referred_columns": fk["referred_columns"],
                            "constrained_columns": fk["constrained_columns"],
                        }
                    )

                schemas[table_name] = {
                    "columns": columns,
                    "primary_key": pk_columns,
                    "foreign_keys": foreign_keys,
                }

            logger.info(f"Retrieved schema information for {len(schemas)} tables")
            return schemas
        except SQLAlchemyError as e:
            logger.error(f"Error getting table schemas: {str(e)}")
            return {}

    async def _convert_nl_to_sql_query(self, query: str) -> Dict[str, Any]:
        """
        Convert natural language query to SQL query using LLM.

        Args:
            query: The natural language query

        Returns:
            A dictionary containing the SQL query and status
        """
        if self.llm is None:
            logger.warning(
                "LLM not initialized, cannot convert natural language to SQL"
            )
            return {
                "success": False,
                "error": "LLM not initialized",
                "sql_query": None,
            }

        try:
            # Format table schemas for the prompt
            schema_str = ""
            for table_name, schema in self.table_schemas.items():
                schema_str += f"Table: {table_name}\n"
                schema_str += "Columns:\n"

                for column in schema["columns"]:
                    pk_marker = (
                        " (Primary Key)"
                        if column["name"] in schema["primary_key"]
                        else ""
                    )
                    schema_str += f"  - {column['name']}: {column['type']}{pk_marker}\n"

                if schema["foreign_keys"]:
                    schema_str += "Foreign Keys:\n"
                    for fk in schema["foreign_keys"]:
                        schema_str += f"  - {', '.join(fk['constrained_columns'])} -> {fk['referred_table']}({', '.join(fk['referred_columns'])})\n"

                schema_str += "\n"

            # Get database-specific syntax guidelines
            db_syntax_guide = self._get_database_syntax_guide()

            # Create the prompt template
            prompt = ChatPromptTemplate.from_messages(
                [
                    (
                        "system",
                        """
                You are an expert in converting natural language queries to SQL queries.
                Your task is to convert the user's natural language query into a valid SQL query.

                Database schema information:
                {schema_info}

                Database-specific syntax guidelines:
                {db_syntax_guide}

                General Guidelines:
                1. Return ONLY the SQL query without any explanations or markdown formatting
                2. Use appropriate SQL syntax based on the database type
                3. Support both English and Turkish language queries
                4. Use appropriate JOINs when querying across multiple tables
                5. Use appropriate WHERE clauses to filter results
                6. If the query is ambiguous, create a reasonable query that would return relevant results
                7. Do not include any explanation, just return the SQL query
                8. Make sure to handle Turkish characters properly
                9. Use LIKE with % wildcards for partial text matching
                10. Follow the database-specific syntax guidelines above
                """,
                    ),
                    (
                        "user",
                        "Convert this natural language query to a SQL query: {query}",
                    ),
                ]
            )

            # Generate the SQL query
            messages = prompt.format_messages(
                query=query, schema_info=schema_str, db_syntax_guide=db_syntax_guide
            )
            response = self.llm.invoke(messages)
            response_text = response.content.strip()

            # Try to extract SQL from the response
            if (
                "```sql" in response_text
                and "```" in response_text.split("```sql", 1)[1]
            ):
                sql_query = (
                    response_text.split("```sql", 1)[1].split("```", 1)[0].strip()
                )
            elif "```" in response_text and "```" in response_text.split("```", 1)[1]:
                sql_query = response_text.split("```", 1)[1].split("```", 1)[0].strip()
            else:
                sql_query = response_text

            # Clean up the SQL query
            sql_query = sql_query.strip()
            if sql_query.startswith("`") and sql_query.endswith("`"):
                sql_query = sql_query[1:-1]

            logger.info(f"LLM generated SQL query: {sql_query}")
            return {
                "success": True,
                "sql_query": sql_query,
            }
        except Exception as e:
            logger.error(f"Error converting query with LLM: {str(e)}")
            return {
                "success": False,
                "error": f"Error using LLM: {str(e)}",
                "sql_query": None,
            }

    @classmethod
    def get_tool_description(cls) -> str:
        return (
            "Executes SQL queries against relational databases to retrieve information. "
            "Uses an LLM to convert natural language queries to SQL queries for more accurate results."
        )
